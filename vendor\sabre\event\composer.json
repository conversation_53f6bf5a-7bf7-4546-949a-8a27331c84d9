{"name": "sabre/event", "description": "sabre/event is a library for lightweight event-based programming", "keywords": ["Events", "EventEmitter", "Promise", "<PERSON>s", "Plugin", "Signal", "Async", "EventLoop", "Reactor", "Coroutine"], "homepage": "http://sabre.io/event/", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^7.1 || ^8.0"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "source": "https://github.com/fruux/sabre-event"}, "autoload": {"psr-4": {"Sabre\\Event\\": "lib/"}, "files": ["lib/coroutine.php", "lib/Loop/functions.php", "lib/Promise/functions.php"]}, "autoload-dev": {"psr-4": {"Sabre\\Event\\": "tests/Event"}}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.0"}, "scripts": {"phpstan": ["phpstan analyse lib tests"], "cs-fixer": ["php-cs-fixer fix"], "phpunit": ["phpunit --configuration tests/phpunit.xml"], "test": ["composer php<PERSON>", "composer cs-fixer", "composer <PERSON><PERSON><PERSON><PERSON>"]}}