<?php

function getDirectorySize($directory) {
    $size = 0;
    $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
    foreach($files as $file){
        $size += $file->getSize();
    }
    return $size;
}

// Get "files" directory size
$filesDirSize = getDirectorySize(__DIR__ . '/files');
$filesDirSizeMB = round($filesDirSize / 1024 / 1024, 2); // Convert to GB

?>
<!DOCTYPE html>
<html>
<head>
    <title>File Upload to WebDAV</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <style>
        body { padding-top: 20px; }
        .container { max-width: 600px; }
        .progress { margin-top: 15px; }
        #resultActions { margin-top: 15px; }
    </style>
</head>
<body>

<div class="container">
    <h2 class="text-center">文件上传</h2>
    <form id="uploadForm">
        <div class="custom-file">
<input type="file" class="custom-file-input" id="fileInput" name="file" onchange="updateFileName()">
<label class="custom-file-label" for="fileInput" id="fileInputLabel">选择文件</label>
        </div>
        <button type="button" class="btn btn-primary btn-block mt-3" onclick="uploadFile()">上传</button>

        <div class="progress">
            <div class="progress-bar" role="progressbar" id="progressBar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <h3 id="status" class="text-center mt-3"></h3>
        <p id="filePath" class="text-center"></p>
        <div id="resultActions" class="text-center" style="display: none;">
            URL: <input type="text" class="form-control" id="uploadResultUrl" readonly>
            <button type="button" class="btn btn-secondary mt-2" onclick="copyUrl()">复制</button>
            <button type="button" class="btn btn-info mt-2" onclick="previewUrl()">预览</button>
        </div>
        <p class="text-center mt-3">最大上传200M，已缓存：<?php echo $filesDirSizeMB; ?> MB</p>
    </form>
</div>

<script src="https://wrhcn001.cachefly.net/webdav/proxy.php?path=2024-01-19/yWIw.js"></script>
<script src="https://wrhcn001.cachefly.net/webdav/proxy.php?path=2024-01-19/kwd0.js"></script>
<script src="https://wrhcn001.cachefly.net/webdav/proxy.php?path=2024-01-19/6G1Q.js"></script>
<script>
function uploadFile() {
    var file = document.getElementById("fileInput").files[0];

    // 检查文件大小是否超过 200MB
    if (file && file.size > 400 * 1024 * 1024) {
        document.getElementById('status').innerHTML = "文件太大，超过了限制";
        return;
    }

    var formData = new FormData();
    formData.append("file", file);

    var xhr = new XMLHttpRequest();
    xhr.open("POST", "upload.php", true);

xhr.upload.onprogress = function(e) {
    if (e.lengthComputable) {
        var percentComplete = (e.loaded / e.total) * 100;
        
        // 获取进度条元素并更新其宽度和 aria-valuenow 属性
        var progressBar = document.getElementById('progressBar');
        progressBar.style.width = percentComplete + '%';
        progressBar.setAttribute('aria-valuenow', percentComplete);

        document.getElementById('status').innerHTML = percentComplete.toFixed(2) + '% uploaded';
    }
};

    xhr.onload = function() {
        if (xhr.status == 200) {
            var response = JSON.parse(xhr.responseText);
            document.getElementById('status').innerHTML = "Upload successful";
            document.getElementById('filePath').innerHTML = response.uploadResult + "<br>" + response.moveResult;

            var resultActions = document.getElementById('resultActions');
            var uploadResultUrl = document.getElementById('uploadResultUrl');
            uploadResultUrl.value = response.uploadResult;
            resultActions.style.display = 'block';
        } else {
            document.getElementById('status').innerHTML = "Upload failed";
        }
    };

    xhr.send(formData);
}

function copyUrl() {
    var copyText = document.getElementById("uploadResultUrl");
    copyText.select();
    document.execCommand("copy");
}

function previewUrl() {
    var url = document.getElementById("uploadResultUrl").value;
    window.open(url, '_blank');
}
function updateFileName() {
    var fileInput = document.getElementById("fileInput");
    var fileInputLabel = document.getElementById("fileInputLabel");
    fileInputLabel.innerHTML = fileInput.files[0].name;
}
</script>

</body>
</html>

