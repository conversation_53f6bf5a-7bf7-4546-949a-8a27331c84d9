{"name": "sabre/dav", "type": "library", "description": "WebDAV Framework for PHP", "keywords": ["Framework", "WebDAV", "CalDAV", "CardDAV", "iCalendar"], "homepage": "http://sabre.io/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "require": {"php": "^7.1.0 || ^8.0", "sabre/vobject": "^4.2.1", "sabre/event": "^5.0", "sabre/xml": "^2.0.1", "sabre/http": "^5.0.5", "sabre/uri": "^2.0", "ext-dom": "*", "ext-pcre": "*", "ext-spl": "*", "ext-simplexml": "*", "ext-mbstring": "*", "ext-ctype": "*", "ext-date": "*", "ext-iconv": "*", "lib-libxml": ">=2.7.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "ext-json": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "monolog/monolog": "^1.27 || ^2.0", "phpstan/phpstan": "^0.12 || ^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6"}, "suggest": {"ext-curl": "*", "ext-pdo": "*", "ext-imap": "*"}, "autoload": {"psr-4": {"Sabre\\": "lib/"}}, "autoload-dev": {"psr-4": {"Sabre\\": "tests/Sabre/"}}, "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "source": "https://github.com/fruux/sabre-dav"}, "bin": ["bin/sabredav", "bin/naturalselection"], "scripts": {"phpstan": ["phpstan analyse lib tests"], "cs-fixer": ["php-cs-fixer fix"], "phpunit": ["phpunit --configuration tests/phpunit.xml"], "test": ["composer php<PERSON>", "composer cs-fixer", "composer <PERSON><PERSON><PERSON><PERSON>"]}}