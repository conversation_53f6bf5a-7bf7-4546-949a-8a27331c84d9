<?php

require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$webdavUrl = $_ENV['WEBDAV_URL'];// WebDAV URL
$username = $_ENV['WEBDAV_USERNAME']; // WebDAV username
$password = $_ENV['WEBDAV_PASSWORD']; // WebDAV password

$message = '';
$url = '';

function getDirectorySize($directory) {
    $size = 0;
    $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
    foreach($files as $file){
        $size += $file->getSize();
    }
    return $size;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    if ($_FILES['file']['error'] === UPLOAD_ERR_OK) {
        if ($_FILES['file']['size'] > 500 * 1024 * 1024) {
            $message = "文件大小超过限制";
        } else {
            $fileTmpPath = $_FILES['file']['tmp_name'];
            $fileName = $_FILES['file']['name'];

            $currentDate = date('Y-m-d');

            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => $webdavUrl . $currentDate,
                CURLOPT_CUSTOMREQUEST => 'HEAD',
                CURLOPT_USERPWD => $username . ':' . $password,
                CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HEADER => true,
            ]);

            $response = curl_exec($curl);

            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if ($httpCode == 404) {
                curl_setopt_array($curl, [
                    CURLOPT_URL => $webdavUrl . $currentDate,
                    CURLOPT_CUSTOMREQUEST => 'MKCOL',
                ]);

                $response = curl_exec($curl);

                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

                if ($httpCode != 201) {
                    echo "Failed to create directory.";
                    curl_close($curl);
                    exit;
                }
            }

            $randomString = substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 4);

            $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);

            $newFileName = $randomString . '.' . $fileExtension;

            curl_setopt_array($curl, [
                CURLOPT_URL => $webdavUrl . $currentDate . '/' . $newFileName,
                CURLOPT_CUSTOMREQUEST => 'PUT',
                CURLOPT_UPLOAD => true,
                CURLOPT_INFILE => fopen($fileTmpPath, 'r'),
                CURLOPT_INFILESIZE => filesize($fileTmpPath),
            ]);

            $response = curl_exec($curl);

            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if ($httpCode == 201) {
                $filePath = $currentDate . '/' . $newFileName;
                $url = 'https://wrhcn001.cachefly.net/webdav/proxy.php?path=' . urlencode($filePath);
                $message = "File uploaded successfully.";

                // Move the file to the local files directory
                $destination = __DIR__ . '/files/' . $filePath;
                if (!is_dir(dirname($destination))) {
                    mkdir(dirname($destination), 0777, true);
                }
                if (move_uploaded_file($fileTmpPath, $destination)) {
                    $message .= " And moved successfully.";
                } else {
                    $message .= " But failed to move.";
                }
            } else {
                $message = "Failed to upload file.";
            }
        }
    } else {
        $message = "Error uploading file.";
    }
}

// Get "files" directory size
$filesDirSize = getDirectorySize(__DIR__ . '/files');
$filesDirSizeMB = round($filesDirSize / 1024 / 1024, 2); // Convert to GB

?>

<!DOCTYPE html>

<html>
<head>
    <title>File Upload</title>
    <style>
body {
    font-family: Arial, sans-serif;
    font-size: 30px; /* Increase font size */
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f0f0f0;
}

body > div {
    position: absolute;
    bottom: calc(100vh / 7); /* Position the content one third up from the bottom */
}
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        form {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
        }
        input[type="file"] {
            margin-bottom: 10px;
        }
        button {
            background-color: #007BFF;
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 30px; /* Increase font size */
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin-top: 20px;
        }
        .url {
            margin-top: 10px;
        }
        .url input {
            font-size: 16px; /* Increase font size */
        }
        input[type="file"] {
            font-size: 30px; /* Increase font size */
            margin-bottom: 10px;
        }
        .url input {
            min-width: 50px; /* Minimum width */
            max-width: 100%; /* Maximum width */
        }
        .upload-limit {
            font-size: 20px; /* Set the font size */
            /*/color: red; /* Set the text color */
            text-align: center; /* Center the text */
            margin-top: 20px; /* Add some space above the text */
        }
    </style>
</head>
<body>
    <div class="container">
        <form method="POST" enctype="multipart/form-data">
            <input type="file" name="file">
            <button type="submit">Upload</button>
            <div class="message"><?php echo $message; ?></div>
            <?php if ($url): ?>
<div class="url">
    URL: <input type="text" value="<?php echo $url; ?>" id="url">
    <button type="button" onclick="copyUrl()">复制</button>
    <button type="button" onclick="previewUrl()">预览</button>
</div>
            <?php endif; ?>
        </form>
        <div class="upload-limit" style="display: flex; justify-content: space-between;">
    <p>最大上传200M，</p>
    <p>已缓存：<?php echo $filesDirSizeMB; ?> MB</p>
</div>
        
    </div>
    <script>
function previewUrl() {
    var url = document.getElementById("url").value;
    window.open(url, '_blank');
}
        function copyUrl() {
            var copyText = document.getElementById("url");
            copyText.select();
            copyText.setSelectionRange(0, 99999); // For mobile devices
            document.execCommand("copy");
            // Removed the alert
        }

        // Add this function
        function adjustInputWidth() {
            var urlInput = document.getElementById("url");
            urlInput.style.width = ((urlInput.value.length + 1) * 8) + 'px'; // Adjust the width based on the length of the value
        }

        // Call the function when the page loads
        window.onload = adjustInputWidth;
    </script>
</body>
</html>

