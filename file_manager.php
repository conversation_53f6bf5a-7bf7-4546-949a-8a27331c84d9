<?php
session_start();

if (!isset($_SESSION['authenticated']) || $_SESSION['authenticated'] !== true) {
    // Redirect to a login page or show an error
    header('Location: index.php');
    exit();
}

function listFiles($directory, $basePath = '') {
    $html = '<ul>';

    foreach (scandir($directory) as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }

        $path = $directory . DIRECTORY_SEPARATOR . $item;
        $relativePath = $basePath . '/' . $item;

        if (is_dir($path)) {
            // 文件夹
            $folderId = 'folder-' . md5($relativePath);
            $html .= '<li class="folder">' . $item . ' 
                <button onclick="toggleFolder(\'' . $folderId . '\')">展开</button> 
                <button onclick="deleteItem(\'' . $relativePath . '\')">删除文件夹</button></li>';
            $html .= '<div id="' . $folderId . '" style="display:none;">' . listFiles($path, $relativePath) . '</div>';
        } else {
            // 文件
            $extension = pathinfo($item, PATHINFO_EXTENSION);
            $previewableExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'pdf', 'txt', 'md', 'html', 'css', 'js', 'json', 'ini', 'yml', 'yaml', 'mp4', 'webm', 'ogg'];

            if (in_array(strtolower($extension), $previewableExtensions)) {
                // 可预览文件
                $html .= '<li>' . $item . ' 
                    <button onclick="previewFile(\'' . $relativePath . '\')">预览</button> 
                    <button onclick="deleteItem(\'' . $relativePath . '\')">删除文件</button></li>';
            } else {
                // 提供下载
                $html .= '<li>' . $item . ' 
                    <a href="files' . $relativePath . '" download>下载</a> 
                    <button onclick="deleteItem(\'' . $relativePath . '\')">删除文件</button></li>';
            }
        }
    }

    $html .= '</ul>';
    return $html;
}

$filesDirectory = __DIR__ . '/files';
echo listFiles($filesDirectory);
?>
