<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

class WebDAVClient {
    private $webdav_url;
    private $webdav_user;
    private $webdav_pass;

    public function __construct($url, $user, $pass) {
        $this->webdav_url = $url;
        $this->webdav_user = $user;
        $this->webdav_pass = $pass;
    }

    private function initCurl($url) {
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_USERPWD, $this->webdav_user . ':' . $this->webdav_pass);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        return $curl;
    }

    public function listFiles() {
        $curl = $this->initCurl($this->webdav_url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'PROPFIND');
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Depth: 1'));

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($response === false) {
            return "Error: " . curl_error($curl);
        }

        $xml = new SimpleXMLElement($response);
        $xml->registerXPathNamespace('d', 'DAV:');
        $result = $xml->xpath('//d:response/d:href');

        $files = array();
        foreach ($result as $href) {
            $files[] = (string)$href;
        }

        return $files;
    }

    public function uploadFile($local_file, $remote_file) {
        $curl = $this->initCurl($this->webdav_url . $remote_file);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($curl, CURLOPT_POSTFIELDS, file_get_contents($local_file));

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        return $http_code == 201 || $http_code == 204;
    }

    public function downloadFile($remote_file) {
        $curl = $this->initCurl($this->webdav_url . $remote_file);
        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($http_code == 200) {
            return $response;
        } else {
            return false;
        }
    }

    public function deleteFile($remote_file) {
        $curl = $this->initCurl($this->webdav_url . $remote_file);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'DELETE');

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        return $http_code == 204;
    }
}

// 使用示例
$webdav_url = 'your_webdav_url';
$webdav_user = 'your_username';
$webdav_pass = 'your_password';

$client = new WebDAVClient($webdav_url, $webdav_user, $webdav_pass);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'upload':
                if (isset($_FILES['file'])) {
                    $result = $client->uploadFile($_FILES['file']['tmp_name'], $_FILES['file']['name']);
                    echo $result ? "File uploaded successfully" : "File upload failed";
                }
                break;
            case 'delete':
                if (isset($_POST['filename'])) {
                    $result = $client->deleteFile($_POST['filename']);
                    echo $result ? "File deleted successfully" : "File deletion failed";
                }
                break;
        }
    }
}

// 显示文件列表和操作表单
?>
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebDAV File Manager</title>
</head>
<body>
    <h1>WebDAV File Manager</h1>
    
    <h2>File List</h2>
    <ul>
    <?php
    $files = $client->listFiles();
    foreach ($files as $file) {
        echo "<li>$file 
            <form method='post' style='display:inline;'>
                <input type='hidden' name='action' value='delete'>
                <input type='hidden' name='filename' value='$file'>
                <input type='submit' value='Delete'>
            </form>
            <a href='?download=$file'>Download</a>
        </li>";
    }
    ?>
    </ul>

    <h2>Upload File</h2>
    <form method="post" enctype="multipart/form-data">
        <input type="hidden" name="action" value="upload">
        <input type="file" name="file" required>
        <input type="submit" value="Upload">
    </form>
</body>
</html>
