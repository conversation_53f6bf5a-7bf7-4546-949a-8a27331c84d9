<?php

/**
 * Microsoft exchange timezones
 * Source:
 * http://msdn.microsoft.com/en-us/library/ms988620%28v=exchg.65%29.aspx.
 *
 * Correct timezones deduced with help from:
 * http://en.wikipedia.org/wiki/List_of_tz_database_time_zones
 *
 * @copyright Copyright (C) fruux GmbH (https://fruux.com/)
 * @license http://sabre.io/license/ Modified BSD License
 */
return [
    'Universal Coordinated Time' => 'UTC',
    'Casablanca, Monrovia' => 'Africa/Casablanca',
    'Greenwich Mean Time: Dublin, Edinburgh, Lisbon, London' => 'Europe/Lisbon',
    'Greenwich Mean Time; Dublin, Edinburgh, London' => 'Europe/London',
    'Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna' => 'Europe/Berlin',
    'Belgrade, Pozsony, Budapest, Ljubljana, Prague' => 'Europe/Prague',
    'Brussels, Copenhagen, Madrid, Paris' => 'Europe/Paris',
    'Paris, Madrid, Brussels, Copenhagen' => 'Europe/Paris',
    'Prague, Central Europe' => 'Europe/Prague',
    'Sarajevo, Skopje, Sofija, Vilnius, Warsaw, Zagreb' => 'Europe/Sarajevo',
    'West Central Africa' => 'Africa/Luanda', // This was a best guess
    'Athens, Istanbul, Minsk' => 'Europe/Athens',
    'Bucharest' => 'Europe/Bucharest',
    'Cairo' => 'Africa/Cairo',
    'Harare, Pretoria' => 'Africa/Harare',
    'Helsinki, Riga, Tallinn' => 'Europe/Helsinki',
    'Israel, Jerusalem Standard Time' => 'Asia/Jerusalem',
    'Baghdad' => 'Asia/Baghdad',
    'Arab, Kuwait, Riyadh' => 'Asia/Kuwait',
    'Moscow, St. Petersburg, Volgograd' => 'Europe/Moscow',
    'East Africa, Nairobi' => 'Africa/Nairobi',
    'Tehran' => 'Asia/Tehran',
    'Abu Dhabi, Muscat' => 'Asia/Muscat', // Best guess
    'Baku, Tbilisi, Yerevan' => 'Asia/Baku',
    'Kabul' => 'Asia/Kabul',
    'Ekaterinburg' => 'Asia/Yekaterinburg',
    'Islamabad, Karachi, Tashkent' => 'Asia/Karachi',
    'Kolkata, Chennai, Mumbai, New Delhi, India Standard Time' => 'Asia/Calcutta',
    'Kathmandu, Nepal' => 'Asia/Kathmandu',
    'Almaty, Novosibirsk, North Central Asia' => 'Asia/Almaty',
    'Astana, Dhaka' => 'Asia/Dhaka',
    'Sri Jayawardenepura, Sri Lanka' => 'Asia/Colombo',
    'Rangoon' => 'Asia/Rangoon',
    'Bangkok, Hanoi, Jakarta' => 'Asia/Bangkok',
    'Krasnoyarsk' => 'Asia/Krasnoyarsk',
    'Beijing, Chongqing, Hong Kong SAR, Urumqi' => 'Asia/Shanghai',
    'Irkutsk, Ulaan Bataar' => 'Asia/Irkutsk',
    'Kuala Lumpur, Singapore' => 'Asia/Singapore',
    'Perth, Western Australia' => 'Australia/Perth',
    'Taipei' => 'Asia/Taipei',
    'Osaka, Sapporo, Tokyo' => 'Asia/Tokyo',
    'Seoul, Korea Standard time' => 'Asia/Seoul',
    'Yakutsk' => 'Asia/Yakutsk',
    'Adelaide, Central Australia' => 'Australia/Adelaide',
    'Darwin' => 'Australia/Darwin',
    'Brisbane, East Australia' => 'Australia/Brisbane',
    'Canberra, Melbourne, Sydney, Hobart (year 2000 only)' => 'Australia/Sydney',
    'Guam, Port Moresby' => 'Pacific/Guam',
    'Hobart, Tasmania' => 'Australia/Hobart',
    'Vladivostok' => 'Asia/Vladivostok',
    'Magadan, Solomon Is., New Caledonia' => 'Asia/Magadan',
    'Auckland, Wellington' => 'Pacific/Auckland',
    'Fiji Islands, Kamchatka, Marshall Is.' => 'Pacific/Fiji',
    'Nuku\'alofa, Tonga' => 'Pacific/Tongatapu',
    'Azores' => 'Atlantic/Azores',
    'Cape Verde Is.' => 'Atlantic/Cape_Verde',
    'Mid-Atlantic' => 'America/Noronha',
    'Brasilia' => 'America/Sao_Paulo', // Best guess
    'Buenos Aires' => 'America/Argentina/Buenos_Aires',
    'Greenland' => 'America/Godthab',
    'Newfoundland' => 'America/St_Johns',
    'Atlantic Time (Canada)' => 'America/Halifax',
    'Caracas, La Paz' => 'America/Caracas',
    'Santiago' => 'America/Santiago',
    'Bogota, Lima, Quito' => 'America/Bogota',
    'Eastern Time (US & Canada)' => 'America/New_York',
    'Indiana (East)' => 'America/Indiana/Indianapolis',
    'Central America' => 'America/Guatemala',
    'Central Time (US & Canada)' => 'America/Chicago',
    'Mexico City, Tegucigalpa' => 'America/Mexico_City',
    'Saskatchewan' => 'America/Edmonton',
    'Arizona' => 'America/Phoenix',
    'Mountain Time (US & Canada)' => 'America/Denver', // Best guess
    'Pacific Time (US & Canada)' => 'America/Los_Angeles', // Best guess
    'Pacific Time (US & Canada); Tijuana' => 'America/Los_Angeles', // Best guess
    'Alaska' => 'America/Anchorage',
    'Hawaii' => 'Pacific/Honolulu',
    'Midway Island, Samoa' => 'Pacific/Midway',
    'Eniwetok, Kwajalein, Dateline Time' => 'Pacific/Kwajalein',
];
