name: continuous-integration
on:
  push:
    branches:
      - master
      - release/*
  pull_request:
jobs:
  unit-testing:
    name: PHPUnit (PHP ${{ matrix.php-versions }})
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php-versions: ['7.2', '7.3', '7.4', '8.0', '8.1']
        coverage: ['pcov']
        code-analysis: ['no']
        include:
          - php-versions: '7.1'
            coverage: 'none'
            code-analysis: 'yes'
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, dom, fileinfo, mysql, redis, opcache
          coverage: ${{ matrix.coverage }}
          tools: composer

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache composer dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          # Use composer.json for key, if composer.lock is not committed.
          # key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Code Analysis (PHP CS-Fixer)
        if: matrix.code-analysis == 'yes'
        run: php vendor/bin/php-cs-fixer fix --dry-run --diff

      - name: Code Analysis (PHPStan)
        if: matrix.code-analysis == 'yes'
        run: composer phpstan

      - name: Test with phpunit
        run: vendor/bin/phpunit --configuration tests/phpunit.xml --coverage-clover clover.xml

      - name: Code Coverage
        uses: codecov/codecov-action@v2
        if: matrix.coverage != 'none'
