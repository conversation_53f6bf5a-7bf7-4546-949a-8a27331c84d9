<?php
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$webdavUrl = $_ENV['WEBDAV_URL'];// WebDAV URL
$username = $_ENV['WEBDAV_USERNAME']; // WebDAV username
$password = $_ENV['WEBDAV_PASSWORD']; // WebDAV password
$cdnurl= $_ENV['CDN_URL'];
$enableImageCDN = isset($_ENV['enable_IMAGECDN']) ? filter_var($_ENV['enable_IMAGECDN'], FILTER_VALIDATE_BOOLEAN) : false;
$imageCDN = isset($_ENV['IMAGECDN']) ? $_ENV['IMAGECDN'] : $cdnurl;

function generateRandomString($length = 4) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

function isImageFile($filename, $filePath = null) {
    // 常见图片扩展名
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif'];

    // 首先检查扩展名
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    if (in_array($extension, $imageExtensions)) {
        return true;
    }

    // 如果没有扩展名或扩展名不匹配，尝试通过MIME类型检测
    if ($filePath && file_exists($filePath)) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        return strpos($mimeType, 'image/') === 0;
    }

    return false;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $isUrlUpload = false;

    // 处理image参数：auto(默认), true(强制使用图片CDN), false(强制使用常规CDN)
    $imageParam = isset($_POST['image']) ? $_POST['image'] : 'auto';

    if (isset($_POST['url'])) {
        $isUrlUpload = true;
        $url = $_POST['url'];
        $file = tempnam(sys_get_temp_dir(), 'download');
        file_put_contents($file, file_get_contents($url));
        $originalName = basename(parse_url($url, PHP_URL_PATH));
    } else {
        $file = $_FILES['file']['tmp_name'];
        $originalName = basename($_FILES['file']['name']);
    }

    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $newFileName = generateRandomString() . '.' . $extension;
    $dateFolder = date('Y-m-d');
    $newwebdavUrl = $webdavUrl . $dateFolder;  // Path adjusted here

    // Create date folder in 'imgs' directory if not exists
    $ch = curl_init($newwebdavUrl);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'MKCOL');
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  // Add this line
    $result = curl_exec($ch);  // Save the result to a variable
    curl_close($ch);

    // Upload file
    $ch = curl_init($newwebdavUrl . '/' . $newFileName);
    curl_setopt($ch, CURLOPT_PUT, true);
    curl_setopt($ch, CURLOPT_INFILE, fopen($file, 'r'));
    curl_setopt($ch, CURLOPT_INFILESIZE, filesize($file));
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  // Add this line
    $result = curl_exec($ch);  // Save the result to a variable
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $uploadResult = "";
    if ($httpCode == 201 || $httpCode == 204) {
        // 根据image参数决定CDN选择逻辑
        $isImage = isImageFile($originalName, $file);
        $selectedCDN = $cdnurl; // 默认使用常规CDN

        if ($imageParam === 'true') {
            // 强制使用图片CDN
            $selectedCDN = $enableImageCDN ? $imageCDN : $cdnurl;
        } elseif ($imageParam === 'false') {
            // 强制使用常规CDN
            $selectedCDN = $cdnurl;
        } else {
            // auto模式：自动判断
            $selectedCDN = ($enableImageCDN && $isImage) ? $imageCDN : $cdnurl;
        }

        $uploadResult = htmlspecialchars($selectedCDN . $dateFolder . '/' . $newFileName);
    } else {
        $uploadResult = "Error uploading file";
    }

    $localDirectory = __DIR__ . '/files/' . $dateFolder;
    if (!file_exists($localDirectory)) {
        mkdir($localDirectory, 0777, true);
    }

    // Move file to local directory
    $localFilePath = $localDirectory . '/' . $newFileName;
    $moveResult = "";
    if ($isUrlUpload) {
        if (rename($file, $localFilePath)) {
            $moveResult = "File moved successful";
        } else {
            $moveResult = "Error moving file";
        }
    } else {
        if (move_uploaded_file($file, $localFilePath)) {
            $moveResult = "File moved successful";
        } else {
            $moveResult = "Error moving file";
        }
    }
}

// 准备响应数据
$isImage = isset($isImage) ? $isImage : false;
$selectedCDN = isset($selectedCDN) ? $selectedCDN : $cdnurl;
$response = [
    'uploadResult' => $uploadResult,
    'moveResult' => $moveResult,
    'isImage' => $isImage,
    'usedImageCDN' => ($selectedCDN === $imageCDN),
    'imageCDN' => $imageCDN,
    'regularCDN' => $cdnurl,
    'fileName' => isset($newFileName) ? $newFileName : '',
    'dateFolder' => isset($dateFolder) ? $dateFolder : '',
    'imageParam' => isset($imageParam) ? $imageParam : 'auto'
];
echo json_encode($response);
?>