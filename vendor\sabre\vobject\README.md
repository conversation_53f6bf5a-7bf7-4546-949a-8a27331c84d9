sabre/vobject
=============

The VObject library allows you to easily parse and manipulate [iCalendar](https://tools.ietf.org/html/rfc5545)
and [vCard](https://tools.ietf.org/html/rfc6350) objects using PHP.

The goal of the VObject library is to create a very complete library, with an easy-to-use API.


Installation
------------

Make sure you have [Composer][1] installed, and then run:

    composer require sabre/vobject "^4.0"

This package requires PHP 5.5. If you need the PHP 5.3/5.4 version of this package instead, use:


    composer require sabre/vobject "^3.4"


Usage
-----

* [Working with vCards](http://sabre.io/vobject/vcard/)
* [Working with iCalendar](http://sabre.io/vobject/icalendar/)



Build status
------------

| branch | status |
| ------ | ------ |
| master | [![Build Status](https://travis-ci.org/sabre-io/vobject.svg?branch=master)](https://travis-ci.org/sabre-io/vobject) |
| 3.5    | [![Build Status](https://travis-ci.org/sabre-io/vobject.svg?branch=3.5)](https://travis-ci.org/sabre-io/vobject) |
| 3.4    | [![Build Status](https://travis-ci.org/sabre-io/vobject.svg?branch=3.4)](https://travis-ci.org/sabre-io/vobject) |
| 3.1    | [![Build Status](https://travis-ci.org/sabre-io/vobject.svg?branch=3.1)](https://travis-ci.org/sabre-io/vobject) |
| 2.1    | [![Build Status](https://travis-ci.org/sabre-io/vobject.svg?branch=2.1)](https://travis-ci.org/sabre-io/vobject) |
| 2.0    | [![Build Status](https://travis-ci.org/sabre-io/vobject.svg?branch=2.0)](https://travis-ci.org/sabre-io/vobject) |



Support
-------

Head over to the [SabreDAV mailing list](http://groups.google.com/group/sabredav-discuss) for any questions.

Made at fruux
-------------

This library is being developed by [fruux](https://fruux.com/). Drop us a line for commercial services or enterprise support.

[1]: https://getcomposer.org/
