<?php
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$webdavUrl = $_ENV['WEBDAV_URL'];
$username = $_ENV['WEBDAV_USERNAME'];
$password = $_ENV['WEBDAV_PASSWORD'];
$cdnurl = $_ENV['CDN_URL'];
$enableImageCDN = isset($_ENV['enable_IMAGECDN']) ? filter_var($_ENV['enable_IMAGECDN'], FILTER_VALIDATE_BOOLEAN) : false;
$imageCDN = isset($_ENV['IMAGECDN']) ? $_ENV['IMAGECDN'] : $cdnurl;

$defaultFolder = 'uploads/default';

function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

function isImageFile($filename, $filePath = null) {
    // 常见图片扩展名
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif'];

    // 首先检查扩展名
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    if (in_array($extension, $imageExtensions)) {
        return true;
    }

    // 如果没有扩展名或扩展名不匹配，尝试通过MIME类型检测
    if ($filePath && file_exists($filePath)) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        return strpos($mimeType, 'image/') === 0;
    }

    return false;
}

function createWebDAVDirectory($url, $username, $password) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'MKCOL');
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_exec($ch);
    curl_close($ch);
}

function ensureWebDAVPathExists($baseUrl, $path, $username, $password) {
    $directories = explode('/', trim($path, '/'));
    $currentPath = $baseUrl;
    
    foreach ($directories as $directory) {
        $currentPath .= '/' . $directory;
        createWebDAVDirectory($currentPath, $username, $password);
    }
}

function createThumbnail($source, $destination, $maxWidth = 400, $maxHeight = 400) {
    list($width, $height, $type) = getimagesize($source);
    
    $ratio = min($maxWidth / $width, $maxHeight / $height);
    $newWidth = $width * $ratio;
    $newHeight = $height * $ratio;
    
    $thumb = imagecreatetruecolor($newWidth, $newHeight);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($source);
            imagealphablending($thumb, false);
            imagesavealpha($thumb, true);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    imagecopyresampled($thumb, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($thumb, $destination, 90);
            break;
        case IMAGETYPE_PNG:
            imagepng($thumb, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($thumb, $destination);
            break;
    }
    
    imagedestroy($sourceImage);
    imagedestroy($thumb);
    
    return true;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $isUrlUpload = false;
    $userFolder = isset($_POST['path']) && !empty($_POST['path']) ? $_POST['path'] : null;
    $fixedFolder = $userFolder ? 'uploads/' . trim($userFolder, '/') : $defaultFolder;
    $keepOriginal = !isset($_POST['original']) || $_POST['original'] !== 'false';

    // 处理image参数：auto(默认), true(强制使用图片CDN), false(强制使用常规CDN)
    $imageParam = isset($_POST['image']) ? $_POST['image'] : 'auto';

    if (isset($_POST['url'])) {
        $isUrlUpload = true;
        $url = $_POST['url'];
        $file = tempnam(sys_get_temp_dir(), 'download');
        file_put_contents($file, file_get_contents($url));
        $originalName = basename(parse_url($url, PHP_URL_PATH));
    } else {
        $file = $_FILES['file']['tmp_name'];
        $originalName = basename($_FILES['file']['name']);
    }

    // Rename file if path is not 'ideogram'
    if ($userFolder !== 'ideogram') {
        $fileInfo = pathinfo($originalName);
        $newFileName = generateUUID() . '.' . $fileInfo['extension'];
    } else {
        $newFileName = $originalName;
    }

    $newwebdavUrl = $webdavUrl . $fixedFolder;
    $uploadResult = "";
    if ($keepOriginal) {
        // 上传原始文件到 WebDAV
        $ch = curl_init($newwebdavUrl . '/' . $newFileName);
        curl_setopt($ch, CURLOPT_PUT, true);
        curl_setopt($ch, CURLOPT_INFILE, fopen($file, 'r'));
        curl_setopt($ch, CURLOPT_INFILESIZE, filesize($file));
        curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode == 201 || $httpCode == 204) {
            // 根据image参数决定CDN选择逻辑
            $isImage = isImageFile($originalName, $file);
            $selectedCDN = $cdnurl; // 默认使用常规CDN

            if ($imageParam === 'true') {
                // 强制使用图片CDN
                $selectedCDN = $enableImageCDN ? $imageCDN : $cdnurl;
            } elseif ($imageParam === 'false') {
                // 强制使用常规CDN
                $selectedCDN = $cdnurl;
            } else {
                // auto模式：自动判断
                $selectedCDN = ($enableImageCDN && $isImage) ? $imageCDN : $cdnurl;
            }

            $uploadResult = htmlspecialchars($selectedCDN . $fixedFolder . '/' . $newFileName);
        } else {
            $uploadResult = "Error uploading file";
        }

        // 移动文件到本地目录
        $localDirectory = __DIR__ . '/files/' . $fixedFolder;
        if (!file_exists($localDirectory)) {
            mkdir($localDirectory, 0777, true);
        }

        $localFilePath = $localDirectory . '/' . $newFileName;
        if ($isUrlUpload) {
            $moveResult = rename($file, $localFilePath) ? "File moved successfully" : "Error moving file";
        } else {
            $moveResult = move_uploaded_file($file, $localFilePath) ? "File moved successfully" : "Error moving file";
        }
    }

    // 创建缩略图
    $thumbnailResult = "";
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    $fileExtension = strtolower(pathinfo($newFileName, PATHINFO_EXTENSION));
    
    if (in_array($fileExtension, $imageExtensions)) {
        $thumbnailDirectory = __DIR__ . '/files/' . $fixedFolder . '/thumbnails';
        if (!file_exists($thumbnailDirectory)) {
            mkdir($thumbnailDirectory, 0777, true);
        }
        
        $thumbnailPath = $thumbnailDirectory . '/' . $newFileName;
        $sourceFile = $keepOriginal ? $localFilePath : $file;
        
        if (createThumbnail($sourceFile, $thumbnailPath)) {
            // 确保 WebDAV 上的缩略图目录存在
            $thumbnailWebdavPath = $fixedFolder . '/thumbnails';
            ensureWebDAVPathExists($webdavUrl, $thumbnailWebdavPath, $username, $password);

            // 上传缩略图到 WebDAV
            $ch = curl_init($webdavUrl . $thumbnailWebdavPath . '/' . $newFileName);
            curl_setopt($ch, CURLOPT_PUT, true);
            curl_setopt($ch, CURLOPT_INFILE, fopen($thumbnailPath, 'r'));
            curl_setopt($ch, CURLOPT_INFILESIZE, filesize($thumbnailPath));
            curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 201 || $httpCode == 204) {
                // 缩略图也使用相同的CDN选择逻辑
                $thumbnailResult = htmlspecialchars($selectedCDN . $fixedFolder . '/thumbnails/' . $newFileName);
            } else {
                $thumbnailResult = "Error uploading thumbnail";
            }
        } else {
            $thumbnailResult = "Error creating thumbnail";
        }
    }

    // 如果不保留原图，删除临时文件
    if (!$keepOriginal && $isUrlUpload) {
        unlink($file);
        unlink($localFilePath);
    }
}

// 返回 JSON 响应
$isImage = isset($isImage) ? $isImage : false;
$selectedCDN = isset($selectedCDN) ? $selectedCDN : $cdnurl;
$response = [
    'isImage' => $isImage,
    'usedImageCDN' => ($selectedCDN === $imageCDN),
    'imageCDN' => $imageCDN,
    'regularCDN' => $cdnurl,
    'imageParam' => isset($imageParam) ? $imageParam : 'auto'
];

if ($keepOriginal) {
    $response['uploadResult'] = $uploadResult;
    $response['moveResult'] = $moveResult;
}

if (!empty($thumbnailResult)) {
    $response['thumbnailResult'] = $thumbnailResult;
}

echo json_encode($response);
?>
