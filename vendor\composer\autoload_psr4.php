<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Sabre\\Xml\\' => array($vendorDir . '/sabre/xml/lib'),
    'Sabre\\VObject\\' => array($vendorDir . '/sabre/vobject/lib'),
    'Sabre\\Uri\\' => array($vendorDir . '/sabre/uri/lib'),
    'Sabre\\HTTP\\' => array($vendorDir . '/sabre/http/lib'),
    'Sabre\\Event\\' => array($vendorDir . '/sabre/event/lib'),
    'Sabre\\' => array($vendorDir . '/sabre/dav/lib'),
    'Root\\WebtextWrhcnTk\\' => array($baseDir . '/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
);
