<?php return array(
    'root' => array(
        'name' => 'root/webtext.wrhcn.tk',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.2',
            'version' => '1.1.2.0',
            'reference' => 'fbd48bce38f73f8a4ec8583362e732e4095e5862',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.2',
            'version' => '1.9.2.0',
            'reference' => '80735db690fe4fc5c76dfa7f9b770634285fa820',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'fe5ea303b0887d5caefd3d431c3e61ad47037001',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'root/webtext.wrhcn.tk' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabre/dav' => array(
            'pretty_version' => '4.6.0',
            'version' => '4.6.0.0',
            'reference' => '554145304b4a026477d130928d16e626939b0b2a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabre/dav',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabre/event' => array(
            'pretty_version' => '5.1.4',
            'version' => '5.1.4.0',
            'reference' => 'd7da22897125d34d7eddf7977758191c06a74497',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabre/event',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabre/http' => array(
            'pretty_version' => '5.1.10',
            'version' => '5.1.10.0',
            'reference' => 'f9f3d1fba8916fa2f4ec25636c4fedc26cb94e02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabre/http',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabre/uri' => array(
            'pretty_version' => '2.3.3',
            'version' => '2.3.3.0',
            'reference' => '7e0e7dfd0b7e14346a27eabd66e843a6e7f1812b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabre/uri',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabre/vobject' => array(
            'pretty_version' => '4.5.4',
            'version' => '4.5.4.0',
            'reference' => 'a6d53a3e5bec85ed3dd78868b7de0f5b4e12f772',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabre/vobject',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabre/xml' => array(
            'pretty_version' => '2.2.6',
            'version' => '2.2.6.0',
            'reference' => '9cde7cdab1e50893cc83b037b40cd47bfde42a2b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabre/xml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '42292d99c55abe617799667f454222c54c60e229',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '6caa57379c4aec19c0a12a38b59b26487dcfe4b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.0',
            'version' => '5.6.0.0',
            'reference' => '2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
