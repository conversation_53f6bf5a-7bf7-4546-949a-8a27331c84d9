![sabre's logo](http://sabre.io/img/logo.png) sabre/dav
=======================================================

Introduction
------------

sabre/dav is the most popular WebDAV framework for PHP. Use it to create WebDAV, CalDAV and CardDAV servers.

Full documentation can be found on the website:

http://sabre.io/


Build status
------------

| branch     | status                                                                    | PHP version        |
|------------|---------------------------------------------------------------------------|--------------------|
| master 4.* | ![CI](https://github.com/sabre-io/dav/actions/workflows/ci.yml/badge.svg) | PHP 7.1 up, 8.0 up |
| 3.2        | unmaintained                                                              | PHP 5.5 to 7.1     |
| 3.1        | unmaintained                                                              | PHP 5.5            |
| 3.0        | unmaintained                                                              | PHP 5.4            |
| 2.1        | unmaintained                                                              | PHP 5.4            |
| 2.0        | unmaintained                                                              | PHP 5.4            |
| 1.8        | unmaintained                                                              | PHP 5.3            |
| 1.7        | unmaintained                                                              | PHP 5.3            |
| 1.6        | unmaintained                                                              | PHP 5.3            |

Documentation
-------------

* [Introduction](http://sabre.io/dav/).
* [Installation](http://sabre.io/dav/install/).


Made at fruux
-------------

SabreDAV is being developed by [fruux](https://fruux.com/). Drop us a line for commercial services or enterprise support.
