<?php
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$webdavUrl = $_ENV['WEBDAV_URL'];// WebDAV URL
$username = $_ENV['WEBDAV_USERNAME']; // WebDAV username
$password = $_ENV['WEBDAV_PASSWORD']; // WebDAV password
$cdnurl= $_ENV['CDN_URL'];

function generateRandomString($length = 4) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $file = $_FILES['file']['tmp_name'];
    $originalName = basename($_FILES['file']['name']);
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $newFileName = generateRandomString() . '.' . $extension;
    $dateFolder = date('Y-m-d');
    $newwebdavUrl = $webdavUrl . $dateFolder;  // Path adjusted here

    // Create date folder in 'imgs' directory if not exists
    $ch = curl_init($newwebdavUrl);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'MKCOL');
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  // Add this line
    $result = curl_exec($ch);  // Save the result to a variable
    curl_close($ch);

    // Upload file
    $ch = curl_init($newwebdavUrl . '/' . $newFileName);
    curl_setopt($ch, CURLOPT_PUT, true);
    curl_setopt($ch, CURLOPT_INFILE, fopen($file, 'r'));
    curl_setopt($ch, CURLOPT_INFILESIZE, filesize($file));
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  // Add this line
    $result = curl_exec($ch);  // Save the result to a variable
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $uploadResult = "";
    if ($httpCode == 201 || $httpCode == 204) {
        $uploadResult = htmlspecialchars($cdnurl . $dateFolder . '/' . $newFileName);
    } else {
        $uploadResult = "Error uploading file";
    }

    $localDirectory = __DIR__ . '/files/' . $dateFolder;
    if (!file_exists($localDirectory)) {
        mkdir($localDirectory, 0777, true);
    }

    // Move file to local directory
    $localFilePath = $localDirectory . '/' . $newFileName;
    $moveResult = "";
    if (move_uploaded_file($file, $localFilePath)) {
        $moveResult = "File moved successful";
    } else {
        $moveResult = "Error moving file";
    }
}
$response = ['uploadResult' => $uploadResult, 'moveResult' => $moveResult];
echo json_encode($response);
?>