<?php

// 递归删除非空文件夹
function deleteDir($dirPath) {
    if (!is_dir($dirPath)) {
        return false;
    }

    foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dirPath, FilesystemIterator::SKIP_DOTS), RecursiveIteratorIterator::CHILD_FIRST) as $path) {
        if ($path->isFile() || $path->isLink()) {
            unlink($path->getPathname());
        } elseif ($path->isDir()) {
            rmdir($path->getPathname());
        }
    }
    rmdir($dirPath); // 删除文件夹自身
    return true;
}

if (isset($_POST['path'])) {
    $relativePath = $_POST['path']; // 前端传递的是相对路径

    // 确保路径是 /files 目录中的
    $baseDirectory = __DIR__ . '/files';
    $fullPath = realpath($baseDirectory . $relativePath);

    // 检查是否路径在允许的 /files 目录中，避免任意文件删除攻击
    if (strpos($fullPath, realpath($baseDirectory)) !== 0) {
        echo json_encode(['success' => false, 'message' => '非法路径']);
        exit;
    }

    if (is_file($fullPath)) {
        if (unlink($fullPath)) {
            echo json_encode(['success' => true, 'message' => '文件删除成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '文件删除失败']);
        }
    } elseif (is_dir($fullPath)) {
        if (deleteDir($fullPath)) {
            echo json_encode(['success' => true, 'message' => '文件夹删除成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '文件夹删除失败']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => '文件或文件夹不存在']);
    }
} else {
    echo json_encode(['success' => false, 'message' => '未提供路径']);
}
?>
