<!DOCTYPE html>
<html>
<head>
    <title>API测试页面</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        button { padding: 10px 15px; margin: 5px; }
        input[type="file"] { margin: 10px 0; }
        select { padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <h1>WebDAV上传API测试</h1>
    
    <div class="test-section">
        <h2>upload.php 测试</h2>
        <input type="file" id="file1" />
        <select id="imageParam1">
            <option value="auto">auto (自动判断)</option>
            <option value="true">true (强制图片CDN)</option>
            <option value="false">false (强制常规CDN)</option>
        </select>
        <button onclick="testUpload('upload.php', 'file1', 'imageParam1', 'result1')">测试上传</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>fixed-upload.php 测试</h2>
        <input type="file" id="file2" />
        <input type="text" id="path2" placeholder="路径 (可选)" />
        <select id="imageParam2">
            <option value="auto">auto (自动判断)</option>
            <option value="true">true (强制图片CDN)</option>
            <option value="false">false (强制常规CDN)</option>
        </select>
        <button onclick="testFixedUpload('fixed-upload.php', 'file2', 'path2', 'imageParam2', 'result2')">测试上传</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>fixed-upload-new.php 测试</h2>
        <input type="file" id="file3" />
        <input type="text" id="path3" placeholder="路径 (可选)" />
        <select id="imageParam3">
            <option value="auto">auto (自动判断)</option>
            <option value="true">true (强制图片CDN)</option>
            <option value="false">false (强制常规CDN)</option>
        </select>
        <button onclick="testFixedUpload('fixed-upload-new.php', 'file3', 'path3', 'imageParam3', 'result3')">测试上传</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        function testUpload(endpoint, fileInputId, imageParamId, resultId) {
            const fileInput = document.getElementById(fileInputId);
            const imageParam = document.getElementById(imageParamId).value;
            const resultDiv = document.getElementById(resultId);
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '请选择文件';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('image', imageParam);
            
            resultDiv.innerHTML = '上传中...';
            
            fetch(endpoint, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                resultDiv.innerHTML = '错误: ' + error.message;
            });
        }
        
        function testFixedUpload(endpoint, fileInputId, pathInputId, imageParamId, resultId) {
            const fileInput = document.getElementById(fileInputId);
            const pathInput = document.getElementById(pathInputId);
            const imageParam = document.getElementById(imageParamId).value;
            const resultDiv = document.getElementById(resultId);
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '请选择文件';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('image', imageParam);
            if (pathInput.value) {
                formData.append('path', pathInput.value);
            }
            
            resultDiv.innerHTML = '上传中...';
            
            fetch(endpoint, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                resultDiv.innerHTML = '错误: ' + error.message;
            });
        }
    </script>
</body>
</html>
