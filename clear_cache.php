<?php
session_start();

if (!isset($_SESSION['authenticated']) || $_SESSION['authenticated'] !== true) {
    // Redirect to a login page or show an error
    header('Location: index.php');
    exit();
}

function deleteDir($dirPath) {
    if (!is_dir($dirPath)) {
        throw new InvalidArgumentException("$dirPath 不是一个目录");
    }

    foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dirPath, FilesystemIterator::SKIP_DOTS), RecursiveIteratorIterator::CHILD_FIRST) as $path) {
        $path->isFile() ? unlink($path->getPathname()) : rmdir($path->getPathname());
    }
}

$filesDirectory = __DIR__ . '/files';
deleteDir($filesDirectory);
mkdir($filesDirectory); // 清空后重新创建空的 /files 目录
?>
