<?php

function getDirectorySize($directory) {
    $size = 0;
    $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
    foreach($files as $file){
        $size += $file->getSize();
    }
    return $size;
}

// Get "files" directory size
$filesDirSize = getDirectorySize(__DIR__ . '/files');
$filesDirSizeMB = round($filesDirSize / 1024 / 1024, 2); // Convert to GB

?>
<!DOCTYPE html>
<html>
<head>
    <title>File Upload to WebDAV</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <style>
        body { padding-top: 20px; }
        .container { max-width: 600px; }
        .progress { margin-top: 15px; }
        #resultActions { margin-top: 15px; }
    </style>
</head>
<body>

<div class="container">
    <h2 class="text-center">文件上传</h2>
    <form id="uploadForm">
        <div class="custom-file">
<input type="file" class="custom-file-input" id="fileInput" name="file" onchange="updateFileName()">
<label class="custom-file-label" for="fileInput" id="fileInputLabel">选择文件</label>
     <button type="button" class="btn btn-primary btn-block mt-1" onclick="uploadFile()">上传</button>
        </div>
 <div style="height: 20px;"></div> <!-- 添加这一行来创建间隙 -->

<div class="custom-file">
    <input type="text" class="form-control" id="urlInput" placeholder="输入文件链接">
    <button type="button" class="btn btn-primary btn-block mt-1" onclick="uploadFileFromUrl()">从链接上传</button>
</div>       

        <div class="progress">
            <div class="progress-bar" role="progressbar" id="progressBar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <h3 id="status" class="text-center mt-3"></h3>
        <p id="filePath" class="text-center"></p>
        <div id="resultActions" class="text-center" style="display: none;">
            URL: <input type="text" class="form-control" id="uploadResultUrl" readonly>
            <button type="button" class="btn btn-secondary mt-2" onclick="copyUrl()">复制</button>
            <button type="button" class="btn btn-info mt-2" onclick="previewUrl()">预览</button>
        </div>
        <p class="text-center mt-3">最大上传200M，已缓存：<?php echo $filesDirSizeMB; ?> MB</p>

        <div id="adminSection" style="display:none; margin-top: 20px;">
            <input type="password" class="form-control" id="adminPassword" placeholder="输入管理员密码">
            <button type="button" class="btn btn-secondary btn-block mt-2" onclick="verifyAdmin()">提交管理员密码</button>
        </div>

        <div class="text-center mt-3">
            <button type="button" class="btn btn-warning" onclick="showAdminSection()">管理员入口</button>
        </div>
    </form>
</div>

<!-- File Manager Modal -->
<div id="fileManagerModal" class="modal" tabindex="-1" role="dialog" style="display:none;">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">文件管理器</h5>
        <button type="button" class="close" onclick="closeFileManager()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <button type="button" class="btn btn-danger mb-2" onclick="clearCache()">清空缓存</button>
        <div id="fileManagerContent">
          <!-- File structure will be dynamically loaded here -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="closeFileManager()">关闭</button>
      </div>
    </div>
  </div>
</div>

<style>
.modal-body {
    max-height: 600px;
    overflow-y: auto;
    text-align: center; /* 使预览内容居中显示 */
}

img.img-fluid {
    max-width: 100%;
    height: auto;
}

pre {
    text-align: left;
    white-space: pre-wrap;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

video {
    max-width: 100%;
    height: auto;
}


 
   .modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1050;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }
    .modal-content {
        padding: 20px;
    }
    .folder {
        cursor: pointer;
    }
</style>

<!-- 模态窗口 -->
<div class="modal fade" id="fileManagerModal" tabindex="-1" role="dialog" aria-labelledby="fileManagerModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="fileManagerModalLabel">文件管理器</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="fileManagerContent">
        <!-- 文件管理器的内容将在这里动态加载 -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" onclick="clearCache()">清空缓存</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>




<script src="https://xrea.988886.xyz/webdav/files/2024-01-19/yWIw.js"></script>
<script src="https://xrea.988886.xyz/webdav/files/2024-01-19/kwd0.js"></script>
<script src="https://xrea.988886.xyz/webdav/files/2024-01-19/6G1Q.js"></script>
<script>
function uploadFile() {
    var file = document.getElementById("fileInput").files[0];

    // 检查文件大小是否超过 200MB
    if (file && file.size > 400 * 1024 * 1024) {
        document.getElementById('status').innerHTML = "文件太大，超过了限制";
        return;
    }

    var formData = new FormData();
    formData.append("file", file);

    var xhr = new XMLHttpRequest();
    xhr.open("POST", "upload.php", true);

xhr.upload.onprogress = function(e) {
    if (e.lengthComputable) {
        var percentComplete = (e.loaded / e.total) * 100;
        
        // 获取进度条元素并更新其宽度和 aria-valuenow 属性
        var progressBar = document.getElementById('progressBar');
        progressBar.style.width = percentComplete + '%';
        progressBar.setAttribute('aria-valuenow', percentComplete);

        document.getElementById('status').innerHTML = percentComplete.toFixed(2) + '% uploaded';
    }
};

    xhr.onload = function() {
        if (xhr.status == 200) {
            var response = JSON.parse(xhr.responseText);
            document.getElementById('status').innerHTML = "Upload successful";
            document.getElementById('filePath').innerHTML = response.uploadResult + "<br>" + response.moveResult;

            var resultActions = document.getElementById('resultActions');
            var uploadResultUrl = document.getElementById('uploadResultUrl');
            uploadResultUrl.value = response.uploadResult;
            resultActions.style.display = 'block';
        } else {
            document.getElementById('status').innerHTML = "Upload failed";
        }
    };

    xhr.send(formData);
}

function copyUrl() {
    var copyText = document.getElementById("uploadResultUrl");
    copyText.select();
    document.execCommand("copy");
}

function previewUrl() {
    var url = document.getElementById("uploadResultUrl").value;
    window.open(url, '_blank');
}
function updateFileName() {
    var fileInput = document.getElementById("fileInput");
    var fileInputLabel = document.getElementById("fileInputLabel");
    fileInputLabel.innerHTML = fileInput.files[0].name;
}
function uploadFileFromUrl() {
    var url = document.getElementById("urlInput").value;

    var xhr = new XMLHttpRequest();
    xhr.open("POST", "upload.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

    xhr.onload = function() {
        if (xhr.status == 200) {
            var response = JSON.parse(xhr.responseText);
            document.getElementById('status').innerHTML = "Upload successful";
            document.getElementById('filePath').innerHTML = response.uploadResult + "<br>" + response.moveResult;

            var resultActions = document.getElementById('resultActions');
            var uploadResultUrl = document.getElementById('uploadResultUrl');
            uploadResultUrl.value = response.uploadResult;
            resultActions.style.display = 'block';
        } else {
            document.getElementById('status').innerHTML = "Upload failed";
        }
    };

    xhr.send("url=" + encodeURIComponent(url));
}


function showAdminSection() {
    document.getElementById("adminSection").style.display = 'block';
}

function verifyAdmin() {
    var adminPassword = document.getElementById("adminPassword").value;
    
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "admin_verify.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

    xhr.onload = function() {
        if (xhr.status == 200) {
            var response = JSON.parse(xhr.responseText);
            if (response.success) {
                // 显示文件管理器
                showFileManager();
            } else {
                alert("密码错误，请重试");
            }
        } else {
            alert("请求失败，请稍后再试");
        }
    };

    xhr.send("password=" + encodeURIComponent(adminPassword));
}

function showFileManager() {
    // 显示模态窗口
    document.getElementById("fileManagerModal").style.display = 'block';
    
    // 使用AJAX动态加载文件列表
    var xhr = new XMLHttpRequest();
    xhr.open("GET", "file_manager.php", true);
    xhr.onload = function() {
        if (xhr.status == 200) {
            document.getElementById("fileManagerContent").innerHTML = xhr.responseText;
        }
    };
    xhr.send();
}

function closeFileManager() {
    document.getElementById("fileManagerModal").style.display = 'none';
}

function toggleFolder(folderId) {
    var folderContent = document.getElementById(folderId);
    if (folderContent.style.display === 'none') {
        folderContent.style.display = 'block';
    } else {
        folderContent.style.display = 'none';
    }
}

function deleteItem(path) {
    if (confirm("确定要删除吗？")) {
        console.log("要删除的路径: " + path);  // 调试用，查看传递的路径是否正确

        var xhr = new XMLHttpRequest();
        xhr.open("POST", "delete_file.php", true);
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

        xhr.onload = function() {
            if (xhr.status == 200) {
                // 重新加载文件管理器内容
                showFileManager();
            } else {
                console.log("删除失败，服务器返回状态: " + xhr.status);
            }
        };

        xhr.onerror = function() {
            console.log("删除请求出错");
        };

        xhr.send("path=" + encodeURIComponent(path));
    }
}



function clearCache() {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "clear_cache.php", true);
    xhr.onload = function() {
        if (xhr.status == 200) {
            alert("缓存已清空");
            showFileManager();
        }
    };
    xhr.send();
}

function previewFile(filePath) {
    var modalBody = document.getElementById("fileManagerContent");
    var backButton = '<button onclick="showFileManager()">返回</button>';

    // 确定文件的类型
    var fileExtension = filePath.split('.').pop().toLowerCase();
    var previewableImageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg'];
    var previewableTextExtensions = ['txt', 'md', 'html', 'css', 'js', 'json', 'ini', 'yml', 'yaml'];
    var videoExtensions = ['mp4', 'webm', 'ogg'];
    var pdfExtension = 'pdf';

    if (previewableImageExtensions.includes(fileExtension)) {
        // 图片文件预览
        modalBody.innerHTML = backButton + '<img src="files' + filePath + '" class="img-fluid" alt="File Preview">';
    } else if (fileExtension === pdfExtension) {
        // PDF文件预览
        modalBody.innerHTML = backButton + '<embed src="files' + filePath + '" type="application/pdf" width="100%" height="600px">';
    } else if (previewableTextExtensions.includes(fileExtension)) {
        // 文本文件预览
        fetch('files' + filePath)
            .then(response => response.text())
            .then(text => {
                modalBody.innerHTML = backButton + '<pre>' + text + '</pre>';
            });
    } else if (videoExtensions.includes(fileExtension)) {
        // 视频文件预览
        modalBody.innerHTML = backButton + 
            '<video controls width="100%">' +
                '<source src="files' + filePath + '" type="video/' + fileExtension + '">' +
                'Your browser does not support the video tag.' +
            '</video>';
    } else {
        // 无法预览的文件，提示下载
        alert('该文件无法预览，将提供下载。');
        window.location.href = 'files' + filePath;
    }
}



</script>

</body>
</html>

