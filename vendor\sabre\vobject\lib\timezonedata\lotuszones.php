<?php

/**
 * The following list are timezone names that could be generated by
 * Lotus / Domino.
 *
 * @copyright Copyright (C) fruux GmbH (https://fruux.com/)
 * @license http://sabre.io/license/ Modified BSD License
 */
return [
    'Dateline' => 'Etc/GMT-12',
    'Samoa' => 'Pacific/Apia',
    'Hawaiian' => 'Pacific/Honolulu',
    'Alaskan' => 'America/Anchorage',
    'Pacific' => 'America/Los_Angeles',
    'Pacific Standard Time' => 'America/Los_Angeles',
    'Mexico Standard Time 2' => 'America/Chihuahua',
    'Mountain' => 'America/Denver',
    // 'Mountain Standard Time' => 'America/Chihuahua', // conflict with windows timezones.
    'US Mountain' => 'America/Phoenix',
    'Canada Central' => 'America/Edmonton',
    'Central America' => 'America/Guatemala',
    'Central' => 'America/Chicago',
    // 'Central Standard Time'  => 'America/Mexico_City', // conflict with windows timezones.
    'Mexico' => 'America/Mexico_City',
    'Eastern' => 'America/New_York',
    'SA Pacific' => 'America/Bogota',
    'US Eastern' => 'America/Indiana/Indianapolis',
    'Venezuela' => 'America/Caracas',
    'Atlantic' => 'America/Halifax',
    'Central Brazilian' => 'America/Manaus',
    'Pacific SA' => 'America/Santiago',
    'SA Western' => 'America/La_Paz',
    'Newfoundland' => 'America/St_Johns',
    'Argentina' => 'America/Argentina/Buenos_Aires',
    'E. South America' => 'America/Belem',
    'Greenland' => 'America/Godthab',
    'Montevideo' => 'America/Montevideo',
    'SA Eastern' => 'America/Belem',
    // 'Mid-Atlantic'           => 'Etc/GMT-2', // conflict with windows timezones.
    'Azores' => 'Atlantic/Azores',
    'Cape Verde' => 'Atlantic/Cape_Verde',
    'Greenwich' => 'Atlantic/Reykjavik', // No I'm serious.. Greenwich is not GMT.
    'Morocco' => 'Africa/Casablanca',
    'Central Europe' => 'Europe/Prague',
    'Central European' => 'Europe/Sarajevo',
    'Romance' => 'Europe/Paris',
    'W. Central Africa' => 'Africa/Lagos', // Best guess
    'W. Europe' => 'Europe/Amsterdam',
    'E. Europe' => 'Europe/Minsk',
    'Egypt' => 'Africa/Cairo',
    'FLE' => 'Europe/Helsinki',
    'GTB' => 'Europe/Athens',
    'Israel' => 'Asia/Jerusalem',
    'Jordan' => 'Asia/Amman',
    'Middle East' => 'Asia/Beirut',
    'Namibia' => 'Africa/Windhoek',
    'South Africa' => 'Africa/Harare',
    'Arab' => 'Asia/Kuwait',
    'Arabic' => 'Asia/Baghdad',
    'E. Africa' => 'Africa/Nairobi',
    'Georgian' => 'Asia/Tbilisi',
    'Russian' => 'Europe/Moscow',
    'Iran' => 'Asia/Tehran',
    'Arabian' => 'Asia/Muscat',
    'Armenian' => 'Asia/Yerevan',
    'Azerbijan' => 'Asia/Baku',
    'Caucasus' => 'Asia/Yerevan',
    'Mauritius' => 'Indian/Mauritius',
    'Afghanistan' => 'Asia/Kabul',
    'Ekaterinburg' => 'Asia/Yekaterinburg',
    'Pakistan' => 'Asia/Karachi',
    'West Asia' => 'Asia/Tashkent',
    'India' => 'Asia/Calcutta',
    'Sri Lanka' => 'Asia/Colombo',
    'Nepal' => 'Asia/Kathmandu',
    'Central Asia' => 'Asia/Dhaka',
    'N. Central Asia' => 'Asia/Almaty',
    'Myanmar' => 'Asia/Rangoon',
    'North Asia' => 'Asia/Krasnoyarsk',
    'SE Asia' => 'Asia/Bangkok',
    'China' => 'Asia/Shanghai',
    'North Asia East' => 'Asia/Irkutsk',
    'Singapore' => 'Asia/Singapore',
    'Taipei' => 'Asia/Taipei',
    'W. Australia' => 'Australia/Perth',
    'Korea' => 'Asia/Seoul',
    'Tokyo' => 'Asia/Tokyo',
    'Yakutsk' => 'Asia/Yakutsk',
    'AUS Central' => 'Australia/Darwin',
    'Cen. Australia' => 'Australia/Adelaide',
    'AUS Eastern' => 'Australia/Sydney',
    'E. Australia' => 'Australia/Brisbane',
    'Tasmania' => 'Australia/Hobart',
    'Vladivostok' => 'Asia/Vladivostok',
    'West Pacific' => 'Pacific/Guam',
    'Central Pacific' => 'Asia/Magadan',
    'Fiji' => 'Pacific/Fiji',
    'New Zealand' => 'Pacific/Auckland',
    'Tonga' => 'Pacific/Tongatapu',
];
