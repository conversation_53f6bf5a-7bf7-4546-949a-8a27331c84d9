<?php

declare(strict_types=1);

namespace Sabre\DAV\Exception;

use Sabre\DAV;

/**
 * ServiceUnavailable.
 *
 * This exception is thrown in case the service
 * is currently not available (e.g. down for maintenance).
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (C) fruux GmbH (https://fruux.com/)
 * @license http://sabre.io/license/ Modified BSD License
 */
class ServiceUnavailable extends DAV\Exception
{
    /**
     * Returns the HTTP statuscode for this exception.
     *
     * @return int
     */
    public function getHTTPCode()
    {
        return 503;
    }
}
