<?php
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$webdavUrl = $_ENV['WEBDAV_URL'];// WebDAV URL
$username = $_ENV['WEBDAV_USERNAME']; // WebDAV username
$password = $_ENV['WEBDAV_PASSWORD']; // WebDAV password

// Get the file path from the request
$filePath = $_GET['path'];

// Define the local file path
$localPath = __DIR__ . '/files/' . $filePath;

// Check if the local file exists
if (file_exists($localPath)) {
    // Get the content type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $contentType = finfo_file($finfo, $localPath);
    finfo_close($finfo);

    // Set the Content-Type header
    header('Content-Type: ' . $contentType);

    // Return the local file content
    echo file_get_contents($localPath);
} else {
    // Initialize cURL
    $curl = curl_init();

    // Set cURL options
    curl_setopt_array($curl, [
        CURLOPT_URL => $webdavUrl . $filePath,
        CURLOPT_USERPWD => $username . ':' . $password,
        CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
        CURLOPT_SSL_VERIFYPEER => false, // Disable SSL verification (only for testing purposes)
        CURLOPT_RETURNTRANSFER => true,
    ]);

    // Execute the cURL request
    $response = curl_exec($curl);

    // Check if the request was successful
    if ($response !== false) {
        // Get the content type
        $contentType = curl_getinfo($curl, CURLINFO_CONTENT_TYPE);

        // Set the Content-Type header
        header('Content-Type: ' . $contentType);

        // Create the local directory if it doesn't exist
        $localDir = dirname($localPath);
        if (!is_dir($localDir)) {
            mkdir($localDir, 0777, true);
        }

        // Save the file content to local
        file_put_contents($localPath, $response);

        // Return the file content
        echo $response;
    } else {
        // Return an error message
        http_response_code(500);
        echo "Error retrieving file.";
    }

    // Close the cURL resource
    curl_close($curl);
}
?>