<?php
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$requestedFilePath = $_GET['file'];
$localFilePath = 'files/' . $requestedFilePath;

// 确定使用哪一套WebDAV信息
$webdavSet = 'default';
$subFolder = explode('/', $requestedFilePath)[0];

if ($subFolder === 'hyw') {
    $webdavSet = 'hyw';
} elseif ($subFolder === 'wwx04') {
    $webdavSet = 'wwx04';
} elseif ($subFolder === 'wicloud') {
    $webdavSet = 'wicloud';
}

// 根据不同的set选择对应的WebDAV信息
switch ($webdavSet) {
    case 'hyw':
        $webdavBaseUrl = $_ENV['WEBDAV_URL_HYW'];
        $webdavUsername = $_ENV['WEBDAV_USERNAME_HYW'];
        $webdavPassword = $_ENV['WEBDAV_PASSWORD_HYW'];
        break;
    case 'wwx04':
        $webdavBaseUrl = $_ENV['WEBDAV_URL_WWX04'];
        $webdavUsername = $_ENV['WEBDAV_USERNAME_WWX04'];
        $webdavPassword = $_ENV['WEBDAV_PASSWORD_WWX04'];
        break;
    case 'wicloud':
        $webdavBaseUrl = $_ENV['WEBDAV_URL_WICLOUD'];
        $webdavUsername = $_ENV['WEBDAV_USERNAME_WICLOUD'];
        $webdavPassword = $_ENV['WEBDAV_PASSWORD_WICLOUD'];
        break;
    default:
        $webdavBaseUrl = $_ENV['WEBDAV_URL'];
        $webdavUsername = $_ENV['WEBDAV_USERNAME'];
        $webdavPassword = $_ENV['WEBDAV_PASSWORD'];
        break;
}

$webdavUrl = $webdavBaseUrl . $requestedFilePath;

// 检查文件是否存在
if (!file_exists($localFilePath)) {
    // 使用cURL从WebDAV下载文件
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webdavUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERPWD, "$webdavUsername:$webdavPassword");
    $fileData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // 检查是否成功下载
    if ($httpCode == 200) {
        // 确保路径存在
        if (!file_exists(dirname($localFilePath))) {
            if (!mkdir(dirname($localFilePath), 0777, true)) {
                header('HTTP/1.0 500 Internal Server Error');
                die("无法创建必要的目录");
            }
        }
        // 保存文件
        file_put_contents($localFilePath, $fileData);
    } else {
        header('HTTP/1.0 404 Not Found');
        die("文件无法从WebDAV下载");
    }
}

// 设置适当的Content-Type
$contentType = mime_content_type($localFilePath);
header('Content-Type: ' . $contentType);

// 根据文件类型确定是否作为附件下载
$downloadableTypes = ['application/zip']; // 可扩展此列表
if (in_array($contentType, $downloadableTypes)) {
    header('Content-Disposition: attachment; filename="' . basename($localFilePath) . '"');
} else {
    header('Content-Disposition: inline; filename="' . basename($localFilePath) . '"');
}

// 输出文件内容
readfile($localFilePath);
exit;
?>
