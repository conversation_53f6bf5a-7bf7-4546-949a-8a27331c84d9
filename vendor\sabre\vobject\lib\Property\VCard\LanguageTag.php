<?php

namespace Sabre\VObject\Property\VCard;

use Sabre\VObject\Property;

/**
 * LanguageTag property.
 *
 * This object represents LANGUAGE-TAG values as used in vCards.
 *
 * @copyright Copyright (C) fruux GmbH (https://fruux.com/)
 * <AUTHOR> (http://evertpot.com/)
 * @license http://sabre.io/license/ Modified BSD License
 */
class LanguageTag extends Property
{
    /**
     * Sets a raw value coming from a mimedir (iCalendar/vCard) file.
     *
     * This has been 'unfolded', so only 1 line will be passed. Unescaping is
     * not yet done, but parameters are not included.
     *
     * @param string $val
     */
    public function setRawMimeDirValue($val)
    {
        $this->setValue($val);
    }

    /**
     * Returns a raw mime-dir representation of the value.
     *
     * @return string
     */
    public function getRawMimeDirValue()
    {
        return $this->getValue();
    }

    /**
     * Returns the type of value.
     *
     * This corresponds to the VALUE= parameter. Every property also has a
     * 'default' valueType.
     *
     * @return string
     */
    public function getValueType()
    {
        return 'LANGUAGE-TAG';
    }
}
