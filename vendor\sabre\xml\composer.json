{"name": "sabre/xml", "description": "sabre/xml is an XML library that you may not hate.", "keywords": ["XML", "XMLReader", "XMLWriter", "DOM"], "homepage": "https://sabre.io/xml/", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^7.1 || ^8.0", "ext-xmlwriter": "*", "ext-xmlreader": "*", "ext-dom": "*", "lib-libxml": ">=2.6.20", "sabre/uri": ">=1.0,<3.0.0"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "source": "https://github.com/fruux/sabre-xml"}, "autoload": {"psr-4": {"Sabre\\Xml\\": "lib/"}, "files": ["lib/Deserializer/functions.php", "lib/Serializer/functions.php"]}, "autoload-dev": {"psr-4": {"Sabre\\Xml\\": "tests/Sabre/Xml/"}}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.0"}, "scripts": {"phpstan": ["phpstan analyse lib tests"], "cs-fixer": ["php-cs-fixer fix"], "phpunit": ["phpunit --configuration tests/phpunit.xml"], "test": ["composer php<PERSON>", "composer cs-fixer", "composer <PERSON><PERSON><PERSON><PERSON>"]}}