<?php
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$webdavUrl = $_ENV['WEBDAV_URL'];  // WebDAV URL
$username = $_ENV['WEBDAV_USERNAME'];  // WebDAV username
$password = $_ENV['WEBDAV_PASSWORD'];  // WebDAV password
$cdnurl = $_ENV['CDN_URL'];  // CDN URL

// 默认的 WebDAV 目录
$defaultFolder = 'uploads/default';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $isUrlUpload = false;

    // 判断是否提供了路径参数
    $userFolder = isset($_POST['path']) && !empty($_POST['path']) ? $_POST['path'] : null;
    // 如果用户未提交 path 参数，则使用默认目录
    $fixedFolder = $userFolder ? 'uploads/' . trim($userFolder, '/') : $defaultFolder;

    if (isset($_POST['url'])) {
        $isUrlUpload = true;
        $url = $_POST['url'];
        $file = tempnam(sys_get_temp_dir(), 'download');
        file_put_contents($file, file_get_contents($url));
        // 使用用户提供的文件名
        $originalName = basename(parse_url($url, PHP_URL_PATH));
    } else {
        // 获取上传的文件
        $file = $_FILES['file']['tmp_name'];
        // 使用用户上传时的文件名
        $originalName = basename($_FILES['file']['name']);
    }

    // 拼接 WebDAV 文件夹路径
    $newwebdavUrl = $webdavUrl . $fixedFolder;

    // 创建 WebDAV 目录（如果不存在）
    $ch = curl_init($newwebdavUrl);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'MKCOL');
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);

    // 上传文件到 WebDAV
    $ch = curl_init($newwebdavUrl . '/' . $originalName);
    curl_setopt($ch, CURLOPT_PUT, true);
    curl_setopt($ch, CURLOPT_INFILE, fopen($file, 'r'));
    curl_setopt($ch, CURLOPT_INFILESIZE, filesize($file));
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // 检查上传结果
    $uploadResult = "";
    if ($httpCode == 201 || $httpCode == 204) {
        // 上传成功，返回固定的 CDN URL 和文件名
        $uploadResult = htmlspecialchars($cdnurl . $fixedFolder . '/' . $originalName);
    } else {
        $uploadResult = "Error uploading file";
    }

    // 创建本地固定目录
    $localDirectory = __DIR__ . '/files/' . $fixedFolder;
    if (!file_exists($localDirectory)) {
        mkdir($localDirectory, 0777, true);
    }

    // 将文件移动到本地固定目录
    $localFilePath = $localDirectory . '/' . $originalName;
    $moveResult = "";
    if ($isUrlUpload) {
        if (rename($file, $localFilePath)) {
            $moveResult = "File moved successfully";
        } else {
            $moveResult = "Error moving file";
        }
    } else {
        if (move_uploaded_file($file, $localFilePath)) {
            $moveResult = "File moved successfully";
        } else {
            $moveResult = "Error moving file";
        }
    }
}

// 返回 JSON 响应
$response = ['uploadResult' => $uploadResult, 'moveResult' => $moveResult];
echo json_encode($response);
?>
